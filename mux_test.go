package mux

import (
	"net/http"
	"testing"
)

func TestNew(t *testing.T) {
	engine := New()
	if engine == nil {
		t.<PERSON><PERSON>("New() should return a non-nil engine")
	}
	if engine.router == nil {
		t.<PERSON>al("Engine should have a router")
	}
}

func TestDefault(t *testing.T) {
	engine := Default()
	if engine == nil {
		t.Fatal("Default() should return a non-nil engine")
	}
	if len(engine.middleware) == 0 {
		t.<PERSON><PERSON>("Default() should include default middleware")
	}
}

func TestRouteRegistration(t *testing.T) {
	engine := New()

	// Test GET route
	engine.GET("/test", func(c *Context) {
		c.String(200, "test")
	})

	handler := engine.findHandler("GET", "/test")
	if handler == nil {
		t.Fatal("GET route should be registered")
	}

	// Test POST route
	engine.POST("/test", func(c *Context) {
		c.String(200, "test")
	})

	handler = engine.findHandler("POST", "/test")
	if handler == nil {
		t.<PERSON>al("POST route should be registered")
	}
}

func TestContext(t *testing.T) {
	// Create a mock request
	req, err := http.NewRequest("GET", "/test?name=john&age=25", nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
		params: make(map[string]string),
		keys:   make(map[string]interface{}),
	}

	// Test query parameters
	name := ctx.Query("name")
	if name != "john" {
		t.Errorf("Expected name to be 'john', got '%s'", name)
	}

	age := ctx.DefaultQuery("age", "0")
	if age != "25" {
		t.Errorf("Expected age to be '25', got '%s'", age)
	}

	missing := ctx.DefaultQuery("missing", "default")
	if missing != "default" {
		t.Errorf("Expected missing to be 'default', got '%s'", missing)
	}

	// Test context storage
	ctx.Set("user", "testuser")
	user, exists := ctx.Get("user")
	if !exists {
		t.Fatal("Key 'user' should exist")
	}
	if user != "testuser" {
		t.Errorf("Expected user to be 'testuser', got '%v'", user)
	}

	// Test status setting
	ctx.Status(404)
	if ctx.response.statusCode != 404 {
		t.Errorf("Expected status code to be 404, got %d", ctx.response.statusCode)
	}
}

func TestMiddleware(t *testing.T) {
	engine := New()

	// Add a test middleware
	engine.Use(func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			next(c)
		}
	})

	if len(engine.middleware) != 1 {
		t.Fatal("Middleware should be added")
	}
}

func TestHTTPCodec(t *testing.T) {
	codec := &httpCodec{
		parser:        newHTTPParser(),
		contentLength: -1,
	}

	// Test simple GET request
	requestData := []byte("GET /test HTTP/1.1\r\nHost: localhost\r\n\r\n")
	offset, req, err := codec.parse(requestData)
	if err != nil {
		t.Fatalf("Failed to parse request: %v", err)
	}
	if req == nil {
		t.Fatal("Request should not be nil")
	}
	if req.Method != "GET" {
		t.Errorf("Expected method to be GET, got %s", req.Method)
	}
	if req.URL.Path != "/test" {
		t.Errorf("Expected path to be /test, got %s", req.URL.Path)
	}
	if offset <= 0 {
		t.Errorf("Expected positive offset, got %d", offset)
	}
}

func TestLoggerMiddleware(t *testing.T) {
	middleware := Logger()
	if middleware == nil {
		t.Fatal("Logger middleware should not be nil")
	}

	executed := false
	handler := middleware(func(c *Context) {
		executed = true
	})

	// Create a mock context
	req, _ := http.NewRequest("GET", "/test", nil)
	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
	}

	handler(ctx)
	if !executed {
		t.Fatal("Handler should be executed")
	}
}

func TestRecoveryMiddleware(t *testing.T) {
	middleware := Recovery()
	if middleware == nil {
		t.Fatal("Recovery middleware should not be nil")
	}

	handler := middleware(func(c *Context) {
		panic("test panic")
	})

	// Create a mock context
	req, _ := http.NewRequest("GET", "/test", nil)
	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
	}

	// This should not panic due to recovery middleware
	handler(ctx)

	// Check if status was set to 500
	if ctx.response.statusCode != 500 {
		t.Errorf("Expected status code to be 500 after panic, got %d", ctx.response.statusCode)
	}
}

func TestCORSMiddleware(t *testing.T) {
	middleware := CORS()
	if middleware == nil {
		t.Fatal("CORS middleware should not be nil")
	}

	executed := false
	handler := middleware(func(c *Context) {
		executed = true
	})

	// Test regular request
	req, _ := http.NewRequest("GET", "/test", nil)
	ctx := &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
	}

	handler(ctx)
	if !executed {
		t.Fatal("Handler should be executed for non-OPTIONS request")
	}

	// Check CORS headers
	if ctx.response.headers["Access-Control-Allow-Origin"] != "*" {
		t.Error("CORS origin header should be set")
	}

	// Test OPTIONS request
	executed = false
	req, _ = http.NewRequest("OPTIONS", "/test", nil)
	ctx = &Context{
		request: req,
		response: &Response{
			headers:    make(map[string]string),
			statusCode: 200,
		},
	}

	handler(ctx)
	if executed {
		t.Fatal("Handler should not be executed for OPTIONS request")
	}
}
