package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"time"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

// MessageToBytes converts a message to bytes (example implementation)
func MessageToBytes(message interface{}) []byte {
	return []byte(fmt.Sprintf("data: %v\n\n", message))
}

// queryMessages simulates querying messages and sending them to a channel
func queryMessages(ctx context.Context, messagesCh chan interface{}) {
	go func() {
		defer close(messagesCh)

		// Simulate streaming data
		messages := []string{
			"Hello from streaming API",
			"This is message 2",
			"Processing data...",
			"Almost done",
			"Final message",
		}

		for i, msg := range messages {
			select {
			case messagesCh <- fmt.Sprintf("[%d] %s", i+1, msg):
				time.Sleep(1 * time.Second) // Simulate processing time
			case <-ctx.Done():
				return
			}
		}
	}()
}

func main() {
	// Create a new engine
	engine := mux.Default()

	// Basic streaming example similar to Gin
	engine.GET("/stream", func(c *mux.Context) {
		// Set appropriate headers for streaming
		c.Header("Content-Type", "text/plain")
		c.<PERSON>er("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")

		// Create a context for cancellation
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Create a channel for messages
		messagesCh := make(chan interface{}, 10)

		// Start querying messages
		queryMessages(ctx, messagesCh)

		// Stream the response
		c.Stream(func(w io.Writer) bool {
			select {
			case message, ok := <-messagesCh:
				if ok {
					w.Write(MessageToBytes(message))
				}
				return ok
			case <-ctx.Done():
				return false
			}
		})
	})

	// Server-Sent Events (SSE) example
	engine.GET("/events", func(c *mux.Context) {
		// Set SSE headers
		c.Header("Content-Type", "text/event-stream")
		c.Header("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")
		c.Header("Access-Control-Allow-Origin", "*")

		// Create a context for cancellation
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Stream SSE events
		c.Stream(func(w io.Writer) bool {
			select {
			case <-time.After(2 * time.Second):
				// Send a timestamp event every 2 seconds
				event := fmt.Sprintf("data: Current time: %s\n\n", time.Now().Format(time.RFC3339))
				w.Write([]byte(event))
				return true
			case <-ctx.Done():
				return false
			}
		})
	})

	// JSON streaming example
	engine.GET("/json-stream", func(c *mux.Context) {
		c.Header("Content-Type", "application/json")
		c.Header("Cache-Control", "no-cache")

		// Create a context for cancellation
		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer cancel()

		counter := 0

		c.Stream(func(w io.Writer) bool {
			select {
			case <-time.After(1 * time.Second):
				counter++
				jsonData := fmt.Sprintf(`{"id": %d, "message": "Streaming JSON data", "timestamp": "%s"}%s`,
					counter, time.Now().Format(time.RFC3339), "\n")
				w.Write([]byte(jsonData))

				// Stop after 10 messages
				return counter < 10
			case <-ctx.Done():
				return false
			}
		})
	})

	// Large data streaming example
	engine.GET("/large-data", func(c *mux.Context) {
		c.Header("Content-Type", "text/plain")
		c.Header("Cache-Control", "no-cache")

		// Simulate streaming large amounts of data
		c.Stream(func(w io.Writer) bool {
			// Generate and stream data in chunks
			for i := 0; i < 1000; i++ {
				data := fmt.Sprintf("Line %d: This is a large data stream with lots of content to demonstrate streaming capabilities.\n", i+1)
				w.Write([]byte(data))

				// Small delay to simulate processing
				time.Sleep(10 * time.Millisecond)
			}
			return false // Stop streaming after all data is sent
		})
	})

	// Start the server
	log.Println("Starting streaming demo server on :8080")
	log.Println("Try these endpoints:")
	log.Println("  http://localhost:8080/stream - Basic streaming example")
	log.Println("  http://localhost:8080/events - Server-Sent Events")
	log.Println("  http://localhost:8080/json-stream - JSON streaming")
	log.Println("  http://localhost:8080/large-data - Large data streaming")

	if err := engine.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
